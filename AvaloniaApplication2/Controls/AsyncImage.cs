using System;
using System.Threading;
using System.Threading.Tasks;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Media;
using Avalonia.Media.Imaging;
using Avalonia.Threading;
using AvaloniaApplication2.Services;

namespace AvaloniaApplication2.Controls
{
    public class AsyncImage : Control
    {
        private static readonly IThumbnailService _thumbnailService = new ThumbnailService();
        private CancellationTokenSource? _loadCancellation;
        private Bitmap? _currentBitmap;

        public static readonly StyledProperty<string?> SourceProperty =
            AvaloniaProperty.Register<AsyncImage, string?>(nameof(Source));

        public static readonly StyledProperty<int> ThumbnailSizeProperty =
            AvaloniaProperty.Register<AsyncImage, int>(nameof(ThumbnailSize), 128);

        public static readonly StyledProperty<Stretch> StretchProperty =
            AvaloniaProperty.Register<AsyncImage, Stretch>(nameof(Stretch), Stretch.UniformToFill);

        public static readonly StyledProperty<IBrush?> PlaceholderBrushProperty =
            AvaloniaProperty.Register<AsyncImage, IBrush?>(nameof(PlaceholderBrush), 
                new SolidColorBrush(Color.FromRgb(64, 64, 64)));

        public string? Source
        {
            get => GetValue(SourceProperty);
            set => SetValue(SourceProperty, value);
        }

        public int ThumbnailSize
        {
            get => GetValue(ThumbnailSizeProperty);
            set => SetValue(ThumbnailSizeProperty, value);
        }

        public Stretch Stretch
        {
            get => GetValue(StretchProperty);
            set => SetValue(StretchProperty, value);
        }

        public IBrush? PlaceholderBrush
        {
            get => GetValue(PlaceholderBrushProperty);
            set => SetValue(PlaceholderBrushProperty, value);
        }

        static AsyncImage()
        {
            SourceProperty.Changed.AddClassHandler<AsyncImage>((x, e) => x.OnSourceChanged());
            ThumbnailSizeProperty.Changed.AddClassHandler<AsyncImage>((x, e) => x.OnSourceChanged());
        }

        private void OnSourceChanged()
        {
            // 取消之前的加载
            _loadCancellation?.Cancel();
            _loadCancellation?.Dispose();
            _loadCancellation = null;

            // 清除当前图像
            _currentBitmap?.Dispose();
            _currentBitmap = null;

            // 触发重绘
            InvalidateVisual();

            // 开始新的加载
            if (!string.IsNullOrWhiteSpace(Source))
            {
                _loadCancellation = new CancellationTokenSource();
                _ = LoadImageAsync(Source, ThumbnailSize, _loadCancellation.Token);
            }
        }

        private async Task LoadImageAsync(string imagePath, int size, CancellationToken cancellationToken)
        {
            try
            {
                var bitmap = await _thumbnailService.GetThumbnailAsync(imagePath, size, cancellationToken);
                
                if (!cancellationToken.IsCancellationRequested && bitmap != null)
                {
                    await Dispatcher.UIThread.InvokeAsync(() =>
                    {
                        if (!cancellationToken.IsCancellationRequested)
                        {
                            _currentBitmap?.Dispose();
                            _currentBitmap = bitmap;
                            InvalidateVisual();
                        }
                        else
                        {
                            bitmap.Dispose();
                        }
                    });
                }
            }
            catch (OperationCanceledException)
            {
                // 正常取消，忽略
            }
            catch
            {
                // 加载失败，保持占位符
            }
        }

        public override void Render(DrawingContext context)
        {
            var bounds = Bounds;
            
            if (_currentBitmap != null)
            {
                // 绘制图像
                var sourceRect = new Rect(_currentBitmap.Size);
                var destRect = CalculateDestRect(sourceRect, bounds, Stretch);
                context.DrawImage(_currentBitmap, sourceRect, destRect);
            }
            else if (PlaceholderBrush != null)
            {
                // 绘制占位符
                context.FillRectangle(PlaceholderBrush, bounds);
            }
        }

        private static Rect CalculateDestRect(Rect sourceRect, Rect bounds, Stretch stretch)
        {
            var sourceSize = sourceRect.Size;
            var destSize = bounds.Size;

            switch (stretch)
            {
                case Stretch.None:
                    return new Rect(
                        (destSize.Width - sourceSize.Width) / 2,
                        (destSize.Height - sourceSize.Height) / 2,
                        sourceSize.Width,
                        sourceSize.Height);

                case Stretch.Fill:
                    return bounds;

                case Stretch.Uniform:
                    {
                        var scale = Math.Min(destSize.Width / sourceSize.Width, destSize.Height / sourceSize.Height);
                        var scaledWidth = sourceSize.Width * scale;
                        var scaledHeight = sourceSize.Height * scale;
                        return new Rect(
                            (destSize.Width - scaledWidth) / 2,
                            (destSize.Height - scaledHeight) / 2,
                            scaledWidth,
                            scaledHeight);
                    }

                case Stretch.UniformToFill:
                    {
                        var scale = Math.Max(destSize.Width / sourceSize.Width, destSize.Height / sourceSize.Height);
                        var scaledWidth = sourceSize.Width * scale;
                        var scaledHeight = sourceSize.Height * scale;
                        return new Rect(
                            (destSize.Width - scaledWidth) / 2,
                            (destSize.Height - scaledHeight) / 2,
                            scaledWidth,
                            scaledHeight);
                    }

                default:
                    return bounds;
            }
        }

        protected override void OnDetachedFromVisualTree(VisualTreeAttachmentEventArgs e)
        {
            base.OnDetachedFromVisualTree(e);
            
            // 清理资源
            _loadCancellation?.Cancel();
            _loadCancellation?.Dispose();
            _loadCancellation = null;
            
            _currentBitmap?.Dispose();
            _currentBitmap = null;
        }
    }
}
