using System;
using System.Globalization;
using System.Threading;
using System.Threading.Tasks;
using Avalonia.Data.Converters;
using Avalonia.Media.Imaging;
using Avalonia.Threading;
using AvaloniaApplication2.Services;

namespace AvaloniaApplication2.Converters
{
    public class AsyncThumbnailConverter : IValueConverter
    {
        private static readonly IThumbnailService _thumbnailService = new ThumbnailService();
        private static readonly Bitmap? _placeholderBitmap = CreatePlaceholder();

        private static Bitmap? CreatePlaceholder()
        {
            // 简化实现，直接返回null，让AsyncImage控件处理占位符
            return null;
        }

        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is not string imagePath || string.IsNullOrWhiteSpace(imagePath))
                return _placeholderBitmap;

            // 获取缩略图大小参数
            var size = 128; // 默认大小
            if (parameter is string sizeStr && int.TryParse(sizeStr, out var parsedSize))
                size = parsedSize;
            else if (parameter is int intSize)
                size = intSize;

            // 异步加载缩略图
            _ = LoadThumbnailAsync(imagePath, size);
            
            // 立即返回占位符
            return _placeholderBitmap;
        }

        private async Task LoadThumbnailAsync(string imagePath, int size)
        {
            try
            {
                var thumbnail = await _thumbnailService.GetThumbnailAsync(imagePath, size);
                
                if (thumbnail != null)
                {
                    // 在UI线程上更新图像
                    await Dispatcher.UIThread.InvokeAsync(() =>
                    {
                        // 触发UI更新 - 这里需要通过其他机制通知UI
                        // 由于IValueConverter不支持异步更新，我们需要使用不同的方法
                        ThumbnailCache.Instance.SetThumbnail(imagePath, size, thumbnail);
                    });
                }
            }
            catch
            {
                // 忽略加载错误
            }
        }

        public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotSupportedException();
        }
    }

    // 缩略图缓存单例，用于在转换器和UI之间共享缩略图
    public class ThumbnailCache
    {
        private static readonly Lazy<ThumbnailCache> _instance = new(() => new ThumbnailCache());
        public static ThumbnailCache Instance => _instance.Value;

        public event EventHandler<ThumbnailLoadedEventArgs>? ThumbnailLoaded;

        public void SetThumbnail(string imagePath, int size, Bitmap thumbnail)
        {
            ThumbnailLoaded?.Invoke(this, new ThumbnailLoadedEventArgs(imagePath, size, thumbnail));
        }
    }

    public class ThumbnailLoadedEventArgs : EventArgs
    {
        public string ImagePath { get; }
        public int Size { get; }
        public Bitmap Thumbnail { get; }

        public ThumbnailLoadedEventArgs(string imagePath, int size, Bitmap thumbnail)
        {
            ImagePath = imagePath;
            Size = size;
            Thumbnail = thumbnail;
        }
    }
}
