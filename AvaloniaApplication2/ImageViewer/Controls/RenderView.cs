using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Globalization;
using System.Runtime.InteropServices;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Media;
using Avalonia.Media.Imaging;
using Avalonia.Platform;
using Avalonia.Media.Immutable;
using Avalonia.Media.TextFormatting;
using ImageViewer.Drawing;

namespace ImageViewer.Controls;

public sealed class RenderView : Control
{
    public static readonly StyledProperty<Bitmap?> SourceProperty =
        AvaloniaProperty.Register<RenderView, Bitmap?>(nameof(Source));

    public static readonly StyledProperty<ToolMode> ToolProperty =
        AvaloniaProperty.Register<RenderView, ToolMode>(nameof(Tool), defaultValue: ToolMode.Edit);

    public static readonly StyledProperty<double> ZoomProperty =
        AvaloniaProperty.Register<RenderView, double>(nameof(Zoom), 1.0);

    public static readonly StyledProperty<IBrush> StrokeProperty =
        AvaloniaProperty.Register<RenderView, IBrush>(nameof(Stroke), Brushes.Red);

    public static readonly StyledProperty<double> StrokeThicknessProperty =
        AvaloniaProperty.Register<RenderView, double>(nameof(StrokeThickness), 1.0);

    public static readonly StyledProperty<bool> ShowPixelGridProperty =
        AvaloniaProperty.Register<RenderView, bool>(nameof(ShowPixelGrid), true);
    public bool ShowPixelGrid
    {
        get => GetValue(ShowPixelGridProperty);
        set => SetValue(ShowPixelGridProperty, value);
    }

    public static readonly StyledProperty<IBrush> GridLineBrushProperty =
        AvaloniaProperty.Register<RenderView, IBrush>(nameof(GridLineBrush),
            new SolidColorBrush(Color.FromArgb(0x96, 0x64, 0x64, 0x64)));
    public IBrush GridLineBrush
    {
        get => GetValue(GridLineBrushProperty);
        set => SetValue(GridLineBrushProperty, value);
    }

    // 以"设备像素"为单位配置线宽，自动换算到 DIP，避免高 DPI 下过粗/过细
    public static readonly StyledProperty<double> GridLineThicknessInDevicePixelsProperty =
        AvaloniaProperty.Register<RenderView, double>(nameof(GridLineThicknessInDevicePixels), 1.0);
    public double GridLineThicknessInDevicePixels
    {
        get => GetValue(GridLineThicknessInDevicePixelsProperty);
        set => SetValue(GridLineThicknessInDevicePixelsProperty, value);
    }

    public Bitmap? Source
    {
        get => GetValue(SourceProperty);
        set => SetValue(SourceProperty, value);
    }

    // Public helper for external callers (e.g., ImageView) to convert from control-space to image-space
    public Point MapViewToImage(Point viewPoint)
    {
        return ViewToImage(viewPoint);
    }

    // Public methods for navigator support
    public void SetViewOffset(Point offset)
    {
        _offset = ClampOffset(offset);
        InvalidateVisual();
    }

    public Point GetViewOffset()
    {
        return _offset;
    }

    public double GetViewScale()
    {
        return _scale;
    }

    public void SetViewScale(double scale)
    {
        _scale = Math.Clamp(scale, MinScale, MaxScale);
        _offset = ClampOffset(_offset);
        UpdateInterpolationMode();
        InvalidateVisual();
    }

    public ToolMode Tool
    {
        get => GetValue(ToolProperty);
        set => SetValue(ToolProperty, value);
    }

    public double Zoom
    {
        get => GetValue(ZoomProperty);
        set => SetValue(ZoomProperty, value);
    }

    public IBrush Stroke
    {
        get => GetValue(StrokeProperty);
        set => SetValue(StrokeProperty, value);
    }

    public double StrokeThickness
    {
        get => GetValue(StrokeThicknessProperty);
        set => SetValue(StrokeThicknessProperty, value);
    }

    public ObservableCollection<IShape> Shapes { get; } = new();

    // 缩放上限/下限
    public double MinScale { get; set; } = 0.05;
    public double MaxScale { get; set; } = 40.0;

    // 滚轮缩放倍率（每一档的乘子）
    public double WheelZoomStep { get; set; } = 1.1;

    // 默认光标
    private readonly Cursor _defaultCursor = new Cursor(StandardCursorType.Cross);

    // 右键拖拽时的状态
    private bool _isPanning;
    private Point _lastPointer;
    private Point _offset = new Point(0, 0);
    private double _scale = 1.0;

    // Interaction state
    private bool _isDrawingRect;
    private RectShape? _activeRect;
    private Point _drawStartImg;

    private bool _isDrawingPoly;
    private PolygonShape? _activePoly;

    private bool _isDrawingCircle;
    private CircleShape? _activeCircle;

    private bool _isDrawingLine;
    private LineShape? _activeLine;

    // Freehand drawing (pencil)
    private bool _isDrawingFreehand;
    private PolygonShape? _activeFreehand;

    // Eraser state
    private bool _isErasing;
    private bool _hasHover;
    private Point _hoverImg;

    private IShape? _hitShape;
    private int _hitHandleIndex;
    private bool _isDraggingHandle;
    private bool _isDraggingBody;
    private Point _lastImg;

    // 用于检测拖拽的字段
    private Point _pressStartImg;
    private bool _hasDragStarted;
    private const double DragThreshold = 3.0; // 像素阈值，超过此距离才认为是拖拽

    // 鼠标像素信息事件：在图像上移动时，通知外界当前坐标与RGB
    public event EventHandler<PixelInfoEventArgs>? PixelInfoChanged;

    // 形状选择状态变化事件：当形状的选择状态发生变化时触发
    public event EventHandler? ShapeSelectionChanged;

    /// <summary>
    /// 获取当前是否正在绘制矩形
    /// </summary>
    public bool IsDrawingRect => _isDrawingRect;

    /// <summary>
    /// 获取当前正在绘制的矩形
    /// </summary>
    public RectShape? ActiveRect => _activeRect;

    /// <summary>
    /// 获取当前是否正在绘制直线
    /// </summary>
    public bool IsDrawingLine => _isDrawingLine;

    /// <summary>
    /// 获取当前正在绘制的直线
    /// </summary>
    public LineShape? ActiveLine => _activeLine;

    /// <summary>
    /// 获取当前选中的形状
    /// </summary>
    public IShape? SelectedShape => Shapes.FirstOrDefault(s => s.Selected);

    private struct LastPixel
    {
        public int X;
        public int Y;
        public byte R;
        public byte G;
        public byte B;
        public bool InBounds;
    }
    private LastPixel _lastPixel;

    public RenderView()
    {
        Focusable = true; // 获得焦点
        ClipToBounds = true; // 超出边界不绘制

        RenderOptions.SetBitmapInterpolationMode(this, BitmapInterpolationMode.LowQuality);

        // 鼠标光标
        Cursor = _defaultCursor;
    }

    protected override void OnPropertyChanged(AvaloniaPropertyChangedEventArgs change)
    {
        base.OnPropertyChanged(change);
        if (change.Property == SourceProperty)
        {
            // 新图默认自适应"居中+适配最小边"
            FitImage();
            UpdateInterpolationMode();
            InvalidateMeasure();
            InvalidateVisual();
        }
        else if (change.Property == ZoomProperty)
        {
            // 同步Zoom属性到内部缩放系统
            if (change.NewValue is double newZoom)
            {
                _scale = Math.Clamp(newZoom, MinScale, MaxScale);
                _offset = ClampOffset(_offset);
                UpdateInterpolationMode();
                InvalidateVisual();
            }
        }
        else if (change.Property == StrokeProperty)
        {
            // Do not retroactively change existing or in-progress shapes
            InvalidateVisual();
        }
        else if (change.Property == StrokeThicknessProperty)
        {
            // Do not retroactively change existing or in-progress shapes
            InvalidateVisual();
        }
        else if (change.Property == ToolProperty)
        {
            // 工具切换时清理所有绘制状态，避免状态混乱
            ClearDrawingStates();
            InvalidateVisual();
        }
    }

    protected override Size MeasureOverride(Size availableSize)
    {
        if (Source is Bitmap bmp)
        {
            // 使用 Bitmap.Size（DIP 尺寸）避免 DPI 下像素/DIP 不一致导致的偏移
            return bmp.Size;
        }
        return base.MeasureOverride(availableSize);
    }

    public override void Render(DrawingContext context)
    {
        base.Render(context);

        DrawCheckerboard(context, Bounds, 18);

        if (Source is null) return;

        // 计算目标矩形：缩放 + 平移
        var iw = Source.Size.Width;
        var ih = Source.Size.Height;
        var dest = new Rect(_offset.X, _offset.Y, iw * _scale, ih * _scale);

        // 设置图像边界供形状约束使用
        ShapeBase.SetImageBounds(new Rect(0, 0, iw, ih));

        // 绘制图像（源矩形用原图大小）
        var src = new Rect(0, 0, iw, ih);

        if (_scale >= 9.0)
        {
            RenderOptions.SetBitmapInterpolationMode(this, BitmapInterpolationMode.None);
            context.DrawImage(Source, src, dest);
            if (ShowPixelGrid && _scale >= 10.0)
            {
                DrawPixelGrid(context, dest, (int)iw, (int)ih);
            }
        }
        else
        {
            RenderOptions.SetBitmapInterpolationMode(this, BitmapInterpolationMode.LowQuality);
            context.DrawImage(Source, src, dest);
        }

        var vp = new Viewport(_scale, _offset);
        // 仅绘制与视口相交的形状
        foreach (var s in Shapes)
        {
            if (!s.GetViewBounds(vp).Intersects(Bounds)) continue;
            s.Paint(context, vp);
        }

        // Draw eraser preview cursor: 自适应大小的橡皮擦预览，类似专业图像处理软件
        if (Tool == ToolMode.Eraser && _hasHover)
        {
            DrawEraserPreview(context, vp);
        }
    }

    protected override void OnPointerMoved(PointerEventArgs e)
    {
        base.OnPointerMoved(e);
        var p = e.GetPosition(this);
        var img = ViewToImage(p);

        // 实时采样像素并上报
        RaisePixelInfo(img);

        // cursor: crosshair for drawing tools; none for eraser (we draw a custom square)
        if (Tool == ToolMode.DrawRect || Tool == ToolMode.DrawPolygon || Tool == ToolMode.DrawCircle || Tool == ToolMode.DrawLine || Tool == ToolMode.DrawFreehand)
            this.Cursor = new Cursor(StandardCursorType.Cross);
        else if (Tool == ToolMode.Eraser)
            this.Cursor = new Cursor(StandardCursorType.None);

        // track hover point for eraser preview
        _hasHover = true;
        _hoverImg = img;

        // 检测拖拽开始：如果鼠标按下但还没开始绘制，检查是否超过拖拽阈值
        if (e.Pointer.Captured == this && !_hasDragStarted && !_isDrawingRect && !_isDrawingCircle && !_isDrawingLine && !_isPanning && !_isDraggingHandle && !_isDraggingBody)
        {
            var dragDistance = Math.Sqrt(Math.Pow(img.X - _pressStartImg.X, 2) + Math.Pow(img.Y - _pressStartImg.Y, 2));
            if (dragDistance > DragThreshold)
            {
                _hasDragStarted = true;
                // 根据当前工具开始相应的绘制操作
                if (Tool == ToolMode.DrawRect)
                {
                    _isDrawingRect = true;
                    _drawStartImg = _pressStartImg;
                    _activeRect = new RectShape(new Rect(_pressStartImg, _pressStartImg))
                    {
                        Selected = true,
                        Fill = null,
                        BorderColor = Stroke,
                        BorderThick = StrokeThickness
                    };
                    ClearSelection();
                    Shapes.Add(_activeRect);
                    InvalidateVisual();
                }
                else if (Tool == ToolMode.DrawCircle)
                {
                    _isDrawingCircle = true;
                    _drawStartImg = _pressStartImg;
                    _activeCircle = new CircleShape(new Rect(_pressStartImg, _pressStartImg))
                    {
                        Selected = true,
                        Fill = null,
                        BorderColor = Stroke,
                        BorderThick = StrokeThickness
                    };
                    ClearSelection();
                    Shapes.Add(_activeCircle);
                    InvalidateVisual();
                }
                else if (Tool == ToolMode.DrawLine)
                {
                    _isDrawingLine = true;
                    _activeLine = new LineShape(_pressStartImg, _pressStartImg)
                    {
                        Selected = true,
                        Fill = null,
                        BorderColor = Stroke,
                        BorderThick = StrokeThickness
                    };
                    ClearSelection();
                    Shapes.Add(_activeLine);
                    InvalidateVisual();
                }
            }
        }

        if (_isPanning)
        {
            var delta = p - _lastPointer;
            _lastPointer = p;

            var newOffset = new Point(_offset.X + delta.X, _offset.Y + delta.Y);
            _offset = ClampOffset(newOffset);
            InvalidateVisual();
            e.Handled = true;
            return;
        }

        if (_isDrawingRect && _activeRect != null)
        {
            var r = new Rect(_drawStartImg, img);
            _activeRect.Rect = NormalizeRect(r);
            InvalidateVisual();
            return;
        }
        if (_isDrawingPoly && _activePoly != null)
        {
            // 更新预览点（末尾点跟随鼠标）
            if (_activePoly.Points.Count >= 1)
            {
                // 应用边界约束
                _activePoly.Points[^1] = ShapeBase.ClampPointToBounds(img);
                InvalidateVisual();
            }
            return;
        }
        if (_isDrawingFreehand && _activeFreehand != null)
        {
            // 仅在距离超过阈值时添加点，降低点数
            const double minDist = 0.5; // image space pixels
            // 应用边界约束
            var constrainedImg = ShapeBase.ClampPointToBounds(img);

            if (_activeFreehand.Points.Count == 0)
            {
                _activeFreehand.Points.Add(constrainedImg);
            }
            else
            {
                var last = _activeFreehand.Points[^1];
                var dx = constrainedImg.X - last.X; var dy = constrainedImg.Y - last.Y;
                if (dx * dx + dy * dy >= minDist * minDist)
                {
                    _activeFreehand.Points.Add(constrainedImg);
                }
            }
            InvalidateVisual();
            return;
        }
        if (_isErasing)
        {
            EraseAt(img, 0); // 半径参数在EraseAt内部自动计算
            InvalidateVisual();
            return;
        }
        if (_isDrawingCircle && _activeCircle != null)
        {
            // 改为通过拖拽矩形确定椭圆边界
            _activeCircle.SetBounds(new Rect(_drawStartImg, img));
            InvalidateVisual();
            return;
        }
        if (_isDrawingLine && _activeLine != null)
        {
            // 终点通过拖拽终点句柄实现
            _activeLine.MoveHandle(1, img);
            InvalidateVisual();
            return;
        }
        if (_isDraggingHandle && _hitShape != null)
        {
            _hitShape.MoveHandle(_hitHandleIndex, img);
            InvalidateVisual();
            return;
        }
        if (_isDraggingBody && _hitShape != null)
        {
            var d = img - _lastImg;
            _hitShape.Move(d);
            _lastImg = img;
            this.Cursor = new Cursor(StandardCursorType.SizeAll);
            InvalidateVisual();
            return;
        }
    }

    protected override void OnPointerExited(PointerEventArgs e)
    {
        base.OnPointerExited(e);
        _hasHover = false;
        InvalidateVisual();
        // avoid lingering SizeAll or custom cursors when leaving
        if (!_isDraggingBody && !_isDraggingHandle)
        {
            this.Cursor = new Cursor(StandardCursorType.Arrow);
        }
    }

    protected override void OnPointerPressed(PointerPressedEventArgs e)
    {
        base.OnPointerPressed(e);
        if (Source is null) return;

        var p = e.GetPosition(this);
        var img = ViewToImage(p);
        this.Focus();

        var props = e.GetCurrentPoint(this).Properties;
        if (props.IsRightButtonPressed)
        {
            _isPanning = true;
            _lastPointer = p;

            Cursor = new Cursor(StandardCursorType.Hand);

            e.Pointer.Capture(this);
            e.Handled = true;
            return;
        }

        // 优先处理绘制工具的双击事件，避免被图像适配拦截
        if (props.IsLeftButtonPressed && e.ClickCount == 2)
        {
            // 多边形绘制模式下的双击：完成多边形
            if (Tool == ToolMode.DrawPolygon && _isDrawingPoly)
            {
                FinishPolygonCommit();
                e.Pointer.Capture(null);
                e.Handled = true;
                return;
            }

            // 其他情况下的双击：回到适配
            FitImage();
            e.Handled = true;
            return;
        }

        if (Tool == ToolMode.DrawRect)
        {
            // 优先命中已存在图形，支持在绘图工具下进行选择/编辑
            var tolSel = CalculateAdaptiveHitTolerance(_scale);
            var hitSel = HitTestShapes(img, tolSel);
            if (hitSel.Target != null)
            {
                _hitShape = hitSel.Target;
                _hitHandleIndex = hitSel.HandleIndex;
                ClearSelection();
                SetShapeSelected(_hitShape, true);
                if (hitSel.Kind == HitKind.Handle && _hitHandleIndex >= 0)
                {
                    (_hitShape as RectShape)?.BeginHandleDrag(_hitHandleIndex);
                    _isDraggingHandle = true;
                    e.Pointer.Capture(this);
                }
                else if (hitSel.Kind == HitKind.Body)
                {
                    _isDraggingBody = true;
                    _lastImg = img;
                    e.Pointer.Capture(this);
                }
                InvalidateVisual();
                return;
            }
            // 记录按下位置，但不立即开始绘制，等待拖拽检测
            _pressStartImg = img;
            _hasDragStarted = false;
            e.Pointer.Capture(this);
            return;
        }

        if (Tool == ToolMode.DrawPolygon)
        {
            var polyProps = e.GetCurrentPoint(this).Properties;
            // 右键结束
            if (_isDrawingPoly && polyProps.IsRightButtonPressed)
            {
                FinishPolygonCommit();
                e.Pointer.Capture(null);
                return;
            }

            // 双击结束逻辑已移到前面统一处理，避免被图像适配拦截

            if (!_isDrawingPoly)
            {
                // 优先命中已存在图形，支持在绘图工具下进行选择/编辑
                var tolSel2 = CalculateAdaptiveHitTolerance(_scale);
                var hitSel2 = HitTestShapes(img, tolSel2);
                if (hitSel2.Target != null)
                {
                    _hitShape = hitSel2.Target;
                    _hitHandleIndex = hitSel2.HandleIndex;
                    ClearSelection();
                    SetShapeSelected(_hitShape, true);
                    if (hitSel2.Kind == HitKind.Handle && _hitHandleIndex >= 0)
                    {
                        (_hitShape as RectShape)?.BeginHandleDrag(_hitHandleIndex);
                        _isDraggingHandle = true;
                        e.Pointer.Capture(this);
                    }
                    else if (hitSel2.Kind == HitKind.Body)
                    {
                        _isDraggingBody = true;
                        _lastImg = img;
                        e.Pointer.Capture(this);
                    }
                    InvalidateVisual();
                    return;
                }
                _isDrawingPoly = true;
                _activePoly = new PolygonShape(new[] { img, img }, closed: true)
                {
                    Selected = true,
                    Fill = null,
                    BorderColor = Stroke,
                    BorderThick = StrokeThickness
                };
                ClearSelection();
                Shapes.Add(_activePoly);
                InvalidateVisual();
                e.Pointer.Capture(this);
                return;
            }
            else if (_activePoly != null)
            {
                // 添加一个新顶点，并附加一个跟随鼠标的预览点
                if (_activePoly.Points.Count >= 1)
                {
                    // 固定上一预览点为真实点，再添加新的预览点
                    _activePoly.Points[^1] = img;
                    _activePoly.Points.Add(img);
                    InvalidateVisual();
                    e.Pointer.Capture(this);
                    return;
                }
            }
        }

        if (Tool == ToolMode.DrawCircle)
        {
            // 优先命中已存在图形，支持在绘图工具下进行选择/编辑
            var tolSel3 = CalculateAdaptiveHitTolerance(_scale);
            var hitSel3 = HitTestShapes(img, tolSel3);
            if (hitSel3.Target != null)
            {
                _hitShape = hitSel3.Target;
                _hitHandleIndex = hitSel3.HandleIndex;
                ClearSelection();
                SetShapeSelected(_hitShape, true);
                if (hitSel3.Kind == HitKind.Handle && _hitHandleIndex >= 0)
                {
                    _isDraggingHandle = true;
                    e.Pointer.Capture(this);
                }
                else if (hitSel3.Kind == HitKind.Body)
                {
                    _isDraggingBody = true;
                    _lastImg = img;
                    e.Pointer.Capture(this);
                }
                InvalidateVisual();
                return;
            }
            // 记录按下位置，但不立即开始绘制，等待拖拽检测
            _pressStartImg = img;
            _hasDragStarted = false;
            e.Pointer.Capture(this);
            return;
        }

        if (Tool == ToolMode.DrawLine)
        {
            // 优先命中已存在图形
            var tolSel4 = CalculateAdaptiveHitTolerance(_scale);
            var hitSel4 = HitTestShapes(img, tolSel4);
            if (hitSel4.Target != null)
            {
                _hitShape = hitSel4.Target;
                _hitHandleIndex = hitSel4.HandleIndex;
                ClearSelection();
                SetShapeSelected(_hitShape, true);
                if (hitSel4.Kind == HitKind.Handle && _hitHandleIndex >= 0)
                {
                    _isDraggingHandle = true;
                    e.Pointer.Capture(this);
                }
                else if (hitSel4.Kind == HitKind.Body)
                {
                    _isDraggingBody = true;
                    _lastImg = img;
                    e.Pointer.Capture(this);
                }
                InvalidateVisual();
                return;
            }
            // 记录按下位置，但不立即开始绘制，等待拖拽检测
            _pressStartImg = img;
            _hasDragStarted = false;
            e.Pointer.Capture(this);
            return;
        }

        if (Tool == ToolMode.DrawFreehand)
        {
            _isDrawingFreehand = true;
            _activeFreehand = new PolygonShape(new[] { img }, closed: false)
            {
                Selected = true,
                Fill = null,
                BorderColor = Stroke,
                BorderThick = StrokeThickness
            };
            _activeFreehand.ShowHandles = false; // hide handles for freehand
            ClearSelection();
            Shapes.Add(_activeFreehand);
            InvalidateVisual();
            e.Pointer.Capture(this);
            return;
        }

        if (Tool == ToolMode.Eraser)
        {
            _isErasing = true;
            // perform initial erase at press
            EraseAt(img, 0); // 半径参数在EraseAt内部自动计算
            InvalidateVisual();
            e.Pointer.Capture(this);
            return;
        }

        // 通用选择逻辑：在所有模式下都支持点击选择/取消选择形状
        var tol = CalculateAdaptiveHitTolerance(_scale); // 使屏幕空间的命中半径大致恒定
        var hit = HitTestShapes(img, tol);
        _hitShape = hit.Target;
        _hitHandleIndex = hit.HandleIndex;

        // 如果点击了形状，选中它
        if (_hitShape != null)
        {
            ClearSelection();
            SetShapeSelected(_hitShape, true);

            // 只有在Edit模式下才允许拖拽编辑
            if (Tool == ToolMode.Edit)
            {
                if (hit.Kind == HitKind.Handle && _hitHandleIndex >= 0)
                {
                    _isDraggingHandle = true;
                    // 告知形状开始句柄拖拽，以记录锚点/角度等上下文
                    (_hitShape as RectShape)?.BeginHandleDrag(_hitHandleIndex);
                    e.Pointer.Capture(this);
                }
                else if (hit.Kind == HitKind.Body)
                {
                    _isDraggingBody = true;
                    _lastImg = img;
                    e.Pointer.Capture(this);
                }
            }
            InvalidateVisual();
        }
        else
        {
            // 点击空白区域，取消所有选择
            ClearSelection();
            InvalidateVisual();
        }
    }

    protected override void OnPointerReleased(PointerReleasedEventArgs e)
    {
        base.OnPointerReleased(e);

        // 处理单击（没有拖拽）的情况：如果鼠标按下但没有开始拖拽，执行选择逻辑
        if (e.Pointer.Captured == this && !_hasDragStarted && !_isPanning && !_isDraggingHandle && !_isDraggingBody && !_isDrawingRect && !_isDrawingCircle && !_isDrawingLine)
        {
            var p = e.GetPosition(this);
            var img = ViewToImage(p);

            // 执行选择逻辑
            var tol = CalculateAdaptiveHitTolerance(_scale);
            var hit = HitTestShapes(img, tol);

            if (hit.Target != null)
            {
                // 点击了形状，选中它
                ClearSelection();
                SetShapeSelected(hit.Target, true);
                InvalidateVisual();
            }
            else
            {
                // 点击空白区域，取消所有选择
                ClearSelection();
                InvalidateVisual();
            }

            e.Pointer.Capture(null);
            e.Handled = true;
            return;
        }

        if (_isPanning)
        {
            _isPanning = false;
            if (e.Pointer.Captured == this)
                e.Pointer.Capture(null);
            Cursor = _defaultCursor;
            e.Handled = true;
            return;
        }

        if (_isDrawingRect)
        {
            _isDrawingRect = false;
            // 绘制完成后不自动保持选中状态，让用户可以看到光标坐标
            if (_activeRect != null)
            {
                _activeRect.Selected = false;
                // 触发选择变化事件，更新状态显示
                ShapeSelectionChanged?.Invoke(this, EventArgs.Empty);
            }
            _activeRect = null;
            // stay in DrawRect for continuous drawing
        }
        if (_isDrawingPoly)
        {
            // 多边形绘制使用点击添加点，不在释放时结束
        }
        if (_isDrawingCircle)
        {
            _isDrawingCircle = false;
            // 绘制完成后不自动保持选中状态，让用户可以看到光标坐标
            if (_activeCircle != null)
            {
                _activeCircle.Selected = false;
                // 触发选择变化事件，更新状态显示
                ShapeSelectionChanged?.Invoke(this, EventArgs.Empty);
            }
            _activeCircle = null;
            // stay in DrawCircle
        }
        if (_isDrawingLine)
        {
            _isDrawingLine = false;
            // 绘制完成后不自动保持选中状态，让用户可以看到光标坐标
            if (_activeLine != null)
            {
                _activeLine.Selected = false;
                // 触发选择变化事件，更新状态显示
                ShapeSelectionChanged?.Invoke(this, EventArgs.Empty);
            }
            _activeLine = null;
            // stay in DrawLine
        }
        if (_isDrawingFreehand)
        {
            _isDrawingFreehand = false;
            if (_activeFreehand != null)
            {
                // 若点数过少则丢弃
                if (_activeFreehand.Points.Count < 2)
                {
                    Shapes.Remove(_activeFreehand);
                }
            }
            _activeFreehand = null;
        }
        if (_isErasing)
        {
            _isErasing = false;
        }
        // 结束句柄拖拽会话，恢复内部状态（锚点/角度）
        if (_isDraggingHandle && _hitShape is RectShape rs)
        {
            rs.EndHandleDrag();
        }
        _isDraggingHandle = false;
        _isDraggingBody = false;
        _hitShape = null;
        _hitHandleIndex = 0;

        // 重置拖拽检测状态
        _hasDragStarted = false;

        e.Pointer.Capture(null);
    }

    private void UpdateCursorForPoint(Point imgPt)
    {
        if (Tool == ToolMode.DrawRect)
        {
            Cursor = new Cursor(StandardCursorType.Cross);
            return;
        }
        var tol = CalculateAdaptiveHitTolerance(_scale);
        var hit = HitTestShapes(imgPt, tol);
        if (hit.Target is ShapeBase sb)
        {
            if (hit.Kind == HitKind.Handle)
            {
                var h = sb.Handles.FirstOrDefault(x => x.Index == hit.HandleIndex);
                Cursor = h?.Cursor ?? new Cursor(StandardCursorType.Arrow);
                return;
            }
            if (hit.Kind == HitKind.Body)
            {
                Cursor = new Cursor(StandardCursorType.SizeAll);
                return;
            }
        }
        Cursor = new Cursor(StandardCursorType.Arrow);
    }

    private HitResult HitTestShapes(Point imgPt, double tol)
    {
        for (int i = Shapes.Count - 1; i >= 0; --i)
        {
            var s = Shapes[i];
            var hit = s.HitTest(imgPt, tol);
            if (hit.Kind != HitKind.None) return hit;
        }
        return HitResult.None;
    }

    private Point ViewToImage(Point p)
    {
        // 使用内部缩放和平移状态进行坐标转换
        var vp = new Viewport(_scale, _offset);
        return vp.ViewToImage(p);
    }

    protected override void OnKeyDown(KeyEventArgs e)
    {
        base.OnKeyDown(e);
        if (e.Key == Key.R) // R 键重置
        {
            FitImage();
            e.Handled = true;
        }
        else if (e.Key == Key.Delete)
        {
            var any = false;
            for (int i = Shapes.Count - 1; i >= 0; --i)
            {
                if (Shapes[i].Selected)
                {
                    Shapes.RemoveAt(i);
                    any = true;
                }
            }
            if (any)
            {
                InvalidateVisual();
                e.Handled = true;
            }
        }
        else if (e.Key == Key.Escape && _isDrawingPoly)
        {
            // 取消当前多边形
            if (_activePoly != null)
            {
                Shapes.Remove(_activePoly);
            }
            _activePoly = null;
            _isDrawingPoly = false;
            InvalidateVisual();
            e.Handled = true;
        }
    }

    private void FinishPolygonCommit()
    {
        if (_activePoly == null)
        {
            _isDrawingPoly = false;
            return;
        }
        // 去掉拖尾的预览点
        if (_activePoly.Points.Count >= 2 && _activePoly.Points[^1] == _activePoly.Points[^2])
        {
            _activePoly.Points.RemoveAt(_activePoly.Points.Count - 1);
        }
        // 至少3点才闭合
        if (_activePoly.Points.Count < 3)
        {
            Shapes.Remove(_activePoly);
        }
        _activePoly = null;
        _isDrawingPoly = false;
        // 保持当前工具为 DrawPolygon，支持连续绘制
        InvalidateVisual();
    }

    private void ClearSelection()
    {
        bool hadSelection = Shapes.Any(s => s.Selected);
        foreach (var s in Shapes) s.Selected = false;
        if (hadSelection)
        {
            ShapeSelectionChanged?.Invoke(this, EventArgs.Empty);
        }
    }

    /// <summary>
    /// 设置形状选择状态并触发选择变化事件
    /// </summary>
    private void SetShapeSelected(IShape shape, bool selected)
    {
        bool wasSelected = shape.Selected;
        shape.Selected = selected;
        if (wasSelected != selected)
        {
            ShapeSelectionChanged?.Invoke(this, EventArgs.Empty);
        }
    }

    /// <summary>
    /// 清理所有绘制状态，在工具切换时调用以避免状态混乱
    /// </summary>
    private void ClearDrawingStates()
    {
        // 清理所有绘制状态标志
        _isDrawingRect = false;
        _isDrawingPoly = false;
        _isDrawingCircle = false;
        _isDrawingLine = false;
        _isDrawingFreehand = false;
        _isErasing = false;
        _isDraggingHandle = false;
        _isDraggingBody = false;
        _isPanning = false;

        // 清理活动形状对象
        _activeRect = null;
        _activePoly = null;
        _activeCircle = null;
        _activeLine = null;
        _activeFreehand = null;

        // 清理命中测试相关状态
        _hitShape = null;
        _hitHandleIndex = 0;

        // 重置光标
        this.Cursor = _defaultCursor;
    }

    protected override void OnPointerEntered(PointerEventArgs e)
    {
        base.OnPointerEntered(e);
        if (Tool == ToolMode.DrawRect || Tool == ToolMode.DrawPolygon || Tool == ToolMode.DrawCircle || Tool == ToolMode.DrawLine)
            this.Cursor = new Cursor(StandardCursorType.Cross);
    }

    // (Duplicate OnPointerExited removed; logic consolidated in the earlier override)

    private static Rect NormalizeRect(Rect r)
    {
        var x1 = Math.Min(r.Left, r.Right);
        var x2 = Math.Max(r.Left, r.Right);
        var y1 = Math.Min(r.Top, r.Bottom);
        var y2 = Math.Max(r.Top, r.Bottom);
        var normalized = new Rect(new Point(x1, y1), new Point(x2, y2));

        // 应用边界约束
        return ShapeBase.ClampRectToBounds(normalized);
    }

    /// <summary>
    /// 绘制橡皮擦预览，自适应大小，类似ImageJ等专业图像处理软件
    /// </summary>
    /// <param name="context">绘制上下文</param>
    /// <param name="vp">视口</param>
    private void DrawEraserPreview(DrawingContext context, Viewport vp)
    {
        // 计算自适应的橡皮擦大小
        var adaptiveRadius = CalculateAdaptiveEraserRadius();

        // 将图像坐标的橡皮擦中心转换为视图坐标
        var centerView = vp.ImageToView(_hoverImg);

        // 计算视图坐标下的橡皮擦半径
        var radiusView = adaptiveRadius * vp.Scale;

        // 绘制橡皮擦预览圆圈
        var previewRect = new Rect(
            centerView.X - radiusView,
            centerView.Y - radiusView,
            radiusView * 2,
            radiusView * 2
        );

        // 橡皮擦预览样式：半透明填充 + 清晰边框，类似ImageJ
        var fill = new SolidColorBrush(Color.FromArgb(30, 255, 100, 100)); // 淡红色填充
        var border = new Pen(Brushes.Red, 1.5); // 红色边框
        var shadow = new Pen(Brushes.Black, 0.5); // 黑色阴影

        // 绘制阴影（稍微偏移）
        var shadowRect = new Rect(previewRect.X + 1, previewRect.Y + 1, previewRect.Width, previewRect.Height);
        context.DrawEllipse(null, shadow, shadowRect);

        // 绘制主体
        context.DrawEllipse(fill, border, previewRect);

        // 绘制中心十字线，便于精确定位
        if (radiusView > 10) // 只在足够大时显示十字线
        {
            var crossSize = Math.Min(radiusView * 0.3, 8);
            var crossPen = new Pen(Brushes.Red, 1);

            // 水平线
            context.DrawLine(crossPen,
                new Point(centerView.X - crossSize, centerView.Y),
                new Point(centerView.X + crossSize, centerView.Y));

            // 垂直线
            context.DrawLine(crossPen,
                new Point(centerView.X, centerView.Y - crossSize),
                new Point(centerView.X, centerView.Y + crossSize));
        }
    }

    /// <summary>
    /// 计算自适应的橡皮擦半径，类似其他形状的自适应逻辑
    /// </summary>
    /// <returns>图像坐标下的橡皮擦半径</returns>
    private double CalculateAdaptiveEraserRadius()
    {
        // 基础橡皮擦大小（图像坐标）
        var baseRadius = Math.Max(1, StrokeThickness / 2.0);

        // 应用类似ShapeBase的自适应逻辑，但针对橡皮擦优化
        // 在低倍缩放时保持合理的图像坐标大小
        // 在高倍缩放时适当减小以提高精度

        if (_scale < 0.5)
        {
            // 极小缩放：增大橡皮擦以便操作
            return baseRadius * 2.0;
        }
        else if (_scale > 4.0)
        {
            // 高倍缩放：减小橡皮擦提高精度
            return baseRadius * 0.7;
        }
        else
        {
            // 正常缩放：使用基础大小
            return baseRadius;
        }
    }

    private void EraseAt(Point imgPt, double radius)
    {
        // 应用边界约束
        imgPt = ShapeBase.ClampPointToBounds(imgPt);

        // 使用自适应半径
        var adaptiveRadius = CalculateAdaptiveEraserRadius();
        if (adaptiveRadius <= 0) adaptiveRadius = 1;
        double r2 = adaptiveRadius * adaptiveRadius;

        for (int i = Shapes.Count - 1; i >= 0; --i)
        {
            var s = Shapes[i];
            if (s is PolygonShape poly && !poly.Closed)
            {
                // Build contiguous segments of points that survive erasing
                var segments = new System.Collections.Generic.List<System.Collections.Generic.List<Point>>();
                System.Collections.Generic.List<Point>? current = null;
                for (int p = 0; p < poly.Points.Count; p++)
                {
                    var pt = poly.Points[p];
                    var dx = pt.X - imgPt.X; var dy = pt.Y - imgPt.Y;
                    bool erased = (dx * dx + dy * dy) <= r2;
                    if (!erased)
                    {
                        if (current == null)
                            current = new System.Collections.Generic.List<Point>();
                        current.Add(pt);
                    }
                    else
                    {
                        if (current != null && current.Count >= 2)
                        {
                            segments.Add(current);
                        }
                        current = null;
                    }
                }
                if (current != null && current.Count >= 2)
                {
                    segments.Add(current);
                }

                // Replace the original polyline with the kept segments
                if (segments.Count == 0)
                {
                    Shapes.RemoveAt(i);
                    continue;
                }
                else if (segments.Count == 1)
                {
                    // mutate in place for minimal churn
                    poly.Points.Clear();
                    poly.Points.AddRange(segments[0]);
                }
                else
                {
                    // remove original and insert new pieces
                    Shapes.RemoveAt(i);
                    foreach (var seg in segments)
                    {
                        var piece = new PolygonShape(seg, closed: false)
                        {
                            Selected = false,
                            Fill = null,
                            BorderColor = poly.BorderColor,
                            BorderThick = poly.BorderThick,
                            ShowHandles = poly.ShowHandles
                        };
                        Shapes.Insert(i, piece);
                        i++; // advance insertion index to retain order
                    }
                }
            }
            else
            {
                // hit-test with tolerance; remove entire shape when hit
                var hit = s.HitTest(imgPt, adaptiveRadius);
                if (hit.Kind != HitKind.None)
                {
                    Shapes.RemoveAt(i);
                }
            }
        }
    }

    protected override void OnPointerWheelChanged(PointerWheelEventArgs e)
    {
        base.OnPointerWheelChanged(e);
        if (Source is null) return;

        // 检查是否按下Ctrl键
        var keyModifiers = e.KeyModifiers;
        bool isCtrlPressed = keyModifiers.HasFlag(KeyModifiers.Control);

        if (isCtrlPressed)
        {
            // Ctrl+滚轮：缩放
            var p = e.GetPosition(this);

            // 计算新缩放
            double factor = e.Delta.Y > 0 ? WheelZoomStep : 1.0 / WheelZoomStep;
            double oldScale = _scale;
            double newScale = Math.Clamp(_scale * factor, MinScale, MaxScale);

            if (Math.Abs(newScale - oldScale) < 1e-6)
                return;

            // 保持光标下的图像点不漂移：offset' = p - (p - offset) * (s'/s)
            var sx = newScale / oldScale;
            var newOffset = new Point(
                p.X - (p.X - _offset.X) * sx,
                p.Y - (p.Y - _offset.Y) * sx
            );

            _scale = newScale;
            _offset = ClampOffset(newOffset);

            // 同步内部缩放到Zoom属性
            Zoom = _scale;

            UpdateInterpolationMode();
            InvalidateVisual();
            e.Handled = true;
        }
        else
        {
            // 普通滚轮：垂直平移
            double panStep = 50.0; // 平移步长
            double deltaY = e.Delta.Y > 0 ? -panStep : panStep;

            var newOffset = new Point(_offset.X, _offset.Y + deltaY);
            _offset = ClampOffset(newOffset);
            InvalidateVisual();
            e.Handled = true;
        }
    }

    protected override void OnSizeChanged(SizeChangedEventArgs e)
    {
        base.OnSizeChanged(e);
        // 你也可以选择 Size 变化时保持当前视图；这里不自动Fit，除非首次加载。
        if (Source != null && _scale == 1.0 && _offset == default)
        {
            FitImage();
        }
        UpdateInterpolationMode();
    }

    public void FitImage()
    {
        if (Source is null || Bounds.Width <= 0 || Bounds.Height <= 0)
        {
            _scale = 1.0;
            _offset = new Point(0, 0);
            InvalidateVisual();
            return;
        }

        // 按较小的缩放适配到控件区域并居中
        double vw = Bounds.Width;
        double vh = Bounds.Height;
        double iw = Source.Size.Width;   // DIP
        double ih = Source.Size.Height;  // DIP

        double s = Math.Min(vw / iw, vh / ih);
        s = Math.Clamp(s, MinScale, MaxScale);

        _scale = s;
        double dx = (vw - iw * s) / 2.0;
        double dy = (vh - ih * s) / 2.0;
        var newOffset = new Point(dx, dy);
        _offset = ClampOffset(newOffset);

        // 同步内部缩放到Zoom属性
        Zoom = _scale;

        InvalidateVisual();
    }

    private void UpdateInterpolationMode()
    {
        var mode = (_scale > 10.0)
            ? BitmapInterpolationMode.None   // 放大：像素级
            : BitmapInterpolationMode.LowQuality;    // 其他：正常插值

        RenderOptions.SetBitmapInterpolationMode(this, mode);
    }

    private void DrawPixelGrid(DrawingContext ctx, Rect dest, int iw, int ih)
    {
        double renderScaling = (this.VisualRoot as TopLevel)?.RenderScaling ?? 1.0;
        double thicknessDIP = GridLineThicknessInDevicePixels / renderScaling;

        var pen = new Pen(GridLineBrush, thicknessDIP);

        // 仅绘制视口内的像素线，减少循环次数
        var view = Bounds.Intersect(dest);

        int x0 = Math.Max(0, (int)Math.Floor((view.X - dest.X) / _scale));
        int x1 = Math.Min(iw, (int)Math.Ceiling((view.Right - dest.X) / _scale));
        int y0 = Math.Max(0, (int)Math.Floor((view.Y - dest.Y) / _scale));
        int y1 = Math.Min(ih, (int)Math.Ceiling((view.Bottom - dest.Y) / _scale));

        // 像素对齐：把线的位置吸附到设备像素栅格，线更锐利
        static double Snap(double x, double rs) => Math.Round(x * rs) / rs;

        // 竖线（像素列分隔）
        for (int xi = x0; xi <= x1; xi++)
        {
            double x = dest.X + xi * _scale;
            double xs = Snap(x, renderScaling);
            ctx.DrawLine(pen, new Point(xs, view.Y), new Point(xs, view.Bottom));
        }

        // 横线（像素行分隔）
        for (int yi = y0; yi <= y1; yi++)
        {
            double y = dest.Y + yi * _scale;
            double ys = Snap(y, renderScaling);
            ctx.DrawLine(pen, new Point(view.X, ys), new Point(view.Right, ys));
        }
    }

    private void DrawCheckerboard(DrawingContext ctx, Rect bounds, int cell = 18)
    {
        var light = new SolidColorBrush(Color.FromRgb(50, 50, 50));
        var dark  = new SolidColorBrush(Color.FromRgb(35, 35, 35));
        for (int y = 0; y < bounds.Height; y += cell)
        {
            for (int x = 0; x < bounds.Width; x += cell)
            {
                bool odd = ((x / cell) + (y / cell)) % 2 == 1;
                ctx.FillRectangle(odd ? dark : light,
                    new Rect(x, y, Math.Min(cell, bounds.Width - x), Math.Min(cell, bounds.Height - y)));
            }
        }
    }

    /// <summary>
    /// 限制偏移量，确保图像不会完全移出控件边界
    /// 允许图像部分超出边界，但至少保持一部分可见
    /// </summary>
    private Point ClampOffset(Point offset)
    {
        if (Source is null || Bounds.Width <= 0 || Bounds.Height <= 0)
            return offset;

        // 计算图像在当前缩放下的尺寸
        double imageWidth = Source.Size.Width * _scale;
        double imageHeight = Source.Size.Height * _scale;

        // 控件尺寸
        double viewWidth = Bounds.Width;
        double viewHeight = Bounds.Height;

        // 定义最小可见区域（图像的一小部分必须保持可见）
        double minVisibleWidth = Math.Min(100, imageWidth * 0.1);   // 至少100像素或图像宽度的10%
        double minVisibleHeight = Math.Min(100, imageHeight * 0.1); // 至少100像素或图像高度的10%

        // 计算允许的偏移范围
        double minX, maxX, minY, maxY;

        if (imageWidth <= viewWidth)
        {
            // 图像比控件窄，居中显示
            double centerX = (viewWidth - imageWidth) / 2;
            minX = maxX = centerX;
        }
        else
        {
            // 图像比控件宽，允许平移但保持部分可见
            minX = viewWidth - imageWidth - minVisibleWidth;  // 图像右边缘可以超出，但要保持左边部分可见
            maxX = minVisibleWidth;                           // 图像左边缘可以超出，但要保持右边部分可见
        }

        if (imageHeight <= viewHeight)
        {
            // 图像比控件矮，居中显示
            double centerY = (viewHeight - imageHeight) / 2;
            minY = maxY = centerY;
        }
        else
        {
            // 图像比控件高，允许平移但保持部分可见
            minY = viewHeight - imageHeight - minVisibleHeight; // 图像底边缘可以超出，但要保持顶部部分可见
            maxY = minVisibleHeight;                            // 图像顶边缘可以超出，但要保持底部部分可见
        }

        // 限制偏移量
        double clampedX = Math.Clamp(offset.X, minX, maxX);
        double clampedY = Math.Clamp(offset.Y, minY, maxY);

        return new Point(clampedX, clampedY);
    }

    // 将图像坐标转换为像素并读取RGB，触发事件
    private void RaisePixelInfo(Point imgPos)
    {
        if (Source is null)
        {
            _lastPixel = new LastPixel { X = -1, Y = -1, R = 0, G = 0, B = 0, InBounds = false };
            PixelInfoChanged?.Invoke(this, new PixelInfoEventArgs(-1, -1, 0, 0, 0, false));
            InvalidateVisual();
            return;
        }

        // 将DIP坐标映射到实际像素（考虑位图DPI缩放）
        double scaleX = Source.PixelSize.Width / Source.Size.Width;
        double scaleY = Source.PixelSize.Height / Source.Size.Height;
        int px = (int)Math.Floor(imgPos.X * scaleX);
        int py = (int)Math.Floor(imgPos.Y * scaleY);

        bool inBounds = px >= 0 && py >= 0 && px < Source.PixelSize.Width && py < Source.PixelSize.Height;
        if (!inBounds)
        {
            _lastPixel = new LastPixel { X = px, Y = py, R = 0, G = 0, B = 0, InBounds = false };
            PixelInfoChanged?.Invoke(this, new PixelInfoEventArgs(px, py, 0, 0, 0, false));
            InvalidateVisual();
            return;
        }

        byte r = 0, g = 0, b = 0;
        // 通过 CopyPixels 读取单像素（BGRA 32bpp）
        var rect = new PixelRect(px, py, 1, 1);
        var buf = new byte[4];
        var handle = GCHandle.Alloc(buf, GCHandleType.Pinned);
        try
        {
            Source.CopyPixels(rect, handle.AddrOfPinnedObject(), 4, 4);
            b = buf[0];
            g = buf[1];
            r = buf[2];
        }
        finally
        {
            if (handle.IsAllocated) handle.Free();
        }

        _lastPixel = new LastPixel { X = px, Y = py, R = r, G = g, B = b, InBounds = true };
        PixelInfoChanged?.Invoke(this, new PixelInfoEventArgs(px, py, r, g, b, true));
        InvalidateVisual();
    }

    /// <summary>
    /// 计算缩放自适应的命中测试容差，类似专业图像处理软件
    /// </summary>
    /// <param name="scale">当前缩放比例</param>
    /// <returns>自适应的命中容差（图像坐标）</returns>
    private static double CalculateAdaptiveHitTolerance(double scale)
    {
        // 专业图像处理软件的命中测试策略：
        // 保持屏幕空间的命中区域大致恒定（约6-8像素）
        const double TargetHitAreaDip = 6.0; // 目标命中区域（DIP）

        // 防止除零错误
        if (scale <= 0.001) scale = 0.001;

        // 将屏幕空间的命中区域转换为图像坐标
        double tolerance = TargetHitAreaDip / scale;

        // 设置合理的范围限制
        const double MinTolerance = 0.5;   // 最小容差（图像坐标）
        const double MaxTolerance = 50.0;  // 最大容差（图像坐标）

        return Math.Clamp(tolerance, MinTolerance, MaxTolerance);
    }

    public sealed class PixelInfoEventArgs : EventArgs
    {
        public int X { get; }
        public int Y { get; }
        public byte R { get; }
        public byte G { get; }
        public byte B { get; }
        public bool InBounds { get; }

        public PixelInfoEventArgs(int x, int y, byte r, byte g, byte b, bool inBounds)
        {
            X = x; Y = y; R = r; G = g; B = b; InBounds = inBounds;
        }
    }
}
