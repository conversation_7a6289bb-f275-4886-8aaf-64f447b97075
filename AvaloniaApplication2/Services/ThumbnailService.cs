using System;
using System.Collections.Concurrent;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Avalonia.Media.Imaging;
using Avalonia.Threading;
using SkiaSharp;

namespace AvaloniaApplication2.Services
{
    public interface IThumbnailService
    {
        Task<Bitmap?> GetThumbnailAsync(string imagePath, int size, CancellationToken cancellationToken = default);
        void ClearCache();
        void SetCacheSize(int maxItems);
    }

    public class ThumbnailService : IThumbnailService, IDisposable
    {
        private readonly ConcurrentDictionary<string, Bitmap?> _cache = new();
        private readonly SemaphoreSlim _semaphore = new(Environment.ProcessorCount, Environment.ProcessorCount);
        private int _maxCacheSize = 1000;
        private volatile bool _disposed = false;

        public void SetCacheSize(int maxItems)
        {
            _maxCacheSize = maxItems;
            CleanupCache();
        }

        public async Task<Bitmap?> GetThumbnailAsync(string imagePath, int size, CancellationToken cancellationToken = default)
        {
            if (_disposed || string.IsNullOrWhiteSpace(imagePath))
                return null;

            var cacheKey = $"{imagePath}_{size}";
            
            // 检查缓存
            if (_cache.TryGetValue(cacheKey, out var cached))
                return cached;

            // 限制并发数量
            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                // 双重检查
                if (_cache.TryGetValue(cacheKey, out cached))
                    return cached;

                // 生成缩略图
                var thumbnail = await GenerateThumbnailAsync(imagePath, size, cancellationToken);
                
                // 缓存结果
                _cache.TryAdd(cacheKey, thumbnail);
                CleanupCache();
                
                return thumbnail;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        private async Task<Bitmap?> GenerateThumbnailAsync(string imagePath, int size, CancellationToken cancellationToken)
        {
            try
            {
                return await Task.Run(() =>
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    if (!File.Exists(imagePath))
                        return null;

                    using var fileStream = File.OpenRead(imagePath);
                    using var skBitmap = SKBitmap.Decode(fileStream);
                    
                    if (skBitmap == null)
                        return null;

                    cancellationToken.ThrowIfCancellationRequested();

                    // 计算缩略图尺寸（保持宽高比）
                    var scale = Math.Min((double)size / skBitmap.Width, (double)size / skBitmap.Height);
                    var newWidth = (int)(skBitmap.Width * scale);
                    var newHeight = (int)(skBitmap.Height * scale);

                    // 创建缩略图
                    using var resized = skBitmap.Resize(new SKImageInfo(newWidth, newHeight), SKFilterQuality.Medium);
                    if (resized == null)
                        return null;

                    cancellationToken.ThrowIfCancellationRequested();

                    // 转换为Avalonia Bitmap
                    using var image = SKImage.FromBitmap(resized);
                    using var data = image.Encode(SKEncodedImageFormat.Png, 80);
                    using var stream = data.AsStream();
                    
                    return new Bitmap(stream);
                }, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                return null;
            }
            catch (Exception)
            {
                // 如果缩略图生成失败，尝试直接加载原图（可能会很慢）
                try
                {
                    return new Bitmap(imagePath);
                }
                catch
                {
                    return null;
                }
            }
        }

        private void CleanupCache()
        {
            if (_cache.Count <= _maxCacheSize)
                return;

            // 简单的LRU清理：移除一半的缓存项
            var toRemove = _cache.Count - _maxCacheSize / 2;
            var removed = 0;
            
            foreach (var kvp in _cache)
            {
                if (removed >= toRemove)
                    break;
                    
                if (_cache.TryRemove(kvp.Key, out var bitmap))
                {
                    bitmap?.Dispose();
                    removed++;
                }
            }
        }

        public void ClearCache()
        {
            foreach (var kvp in _cache)
            {
                kvp.Value?.Dispose();
            }
            _cache.Clear();
        }

        public void Dispose()
        {
            if (_disposed)
                return;
                
            _disposed = true;
            ClearCache();
            _semaphore?.Dispose();
        }
    }
}
