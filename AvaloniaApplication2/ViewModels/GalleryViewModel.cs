using AvaloniaApplication2.Models;
using System.Collections.ObjectModel;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System;
using System.Threading;
using System.Threading.Tasks;
using Avalonia.Threading;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using System.Collections.Specialized;
using System.ComponentModel;

namespace AvaloniaApplication2.ViewModels
{
    public class GalleryViewModel : ViewModelBase
    {
        public ObservableCollection<GalleryItem> GalleryItems { get; }
        private int _thumbnailSize = 138;
        public int ThumbnailSize
        {
            get => _thumbnailSize;
            set => SetProperty(ref _thumbnailSize, value);
        }

        // Current/selection state for left sidebar binding
        private GalleryItem? _currentItem;
        public GalleryItem? CurrentItem
        {
            get => _currentItem;
            set
            {
                if (SetProperty(ref _currentItem, value))
                {
                    RaiseComputedChanged();
                }
            }
        }

        public int TotalCount => GalleryItems.Count;
        public int SelectedCount => GalleryItems.Count(i => i.IsSelected);
        public int CurrentIndex1Based => CurrentItem == null ? 0 : (GalleryItems.IndexOf(CurrentItem) + 1);
        public string? CurrentName => CurrentItem?.Name;
        public string? CurrentPath => CurrentItem?.ImagePath;

        // Annotation progress: images with TagId > 0 are considered annotated
        public int AnnotatedCount => GalleryItems.Count(i => i.TagId > 0);
        public int AnnotationProgressPercent => TotalCount == 0 ? 0 : (int)Math.Round(AnnotatedCount * 100.0 / TotalCount);
        public string AnnotationProgressText => $"标注 {AnnotatedCount}/{TotalCount}（{AnnotationProgressPercent}%）";

        // Display strings for convenience
        public string ImageCountText => $"图像 {CurrentIndex1Based} / {TotalCount}";
        public string SelectedSummaryText => $"{SelectedCount} / {TotalCount} 个图像";

        // Commands
        public IRelayCommand<GalleryItem> ToggleSelectCommand { get; }
        public IRelayCommand RemoveSelectedCommand { get; }
        public IRelayCommand CycleThumbnailSizeCommand { get; }
        public IRelayCommand AdjustBrightnessCommand { get; }
        public IRelayCommand AdjustContrastCommand { get; }
        public IRelayCommand<TagCategory> AssignTagToSelectedCommand { get; }
        public IRelayCommand AddTagCommand { get; }
        public IRelayCommand<TagCategory> DeleteTagCommand { get; }

        // Tags
        public ObservableCollection<TagCategory> Tags { get; } = new();
        public List<string> PresetColors { get; } = new() { "#9E9E9E", "#4CAF50", "#FFC107", "#2196F3", "#E91E63", "#FF5722", "#9C27B0", "#00BCD4" };
        public List<string> PresetIcons { get; } = new()
        {
            "TagOutline",
            "CheckBold",
            "AlertCircleOutline",
            "BugOutline",
            "FlagOutline",
            "LabelOutline",
            "Image",
        };
        private int _nextTagId = 1; // 0 reserved for 无标签
        private string _newTagName = string.Empty;
        public string NewTagName { get => _newTagName; set => SetProperty(ref _newTagName, value); }
        private string _newTagColor = "#4CAF50";
        public string NewTagColor { get => _newTagColor; set => SetProperty(ref _newTagColor, value); }
        private string _newTagIcon = "TagOutline";
        public string NewTagIcon { get => _newTagIcon; set => SetProperty(ref _newTagIcon, value); }
        private bool _newTagIsOk; // true=OK(ThumbUp), false=NG(ThumbDown)
        public bool NewTagIsOk
        {
            get => _newTagIsOk;
            set
            {
                if (SetProperty(ref _newTagIsOk, value))
                {
                    NewTagIcon = _newTagIsOk ? "ThumbUp" : "ThumbDown";
                }
            }
        }

        public GalleryViewModel()
        {
            GalleryItems = new ObservableCollection<GalleryItem>();

            // Track collection changes to update computed properties and hook per-item changes
            GalleryItems.CollectionChanged += OnGalleryItemsChanged;

            // Default tags: 0 无标签
            Tags.Add(new TagCategory { Id = 0, Name = "无标签", Color = "#616161", IconKind = "TagOutline" });

            // Init commands
            ToggleSelectCommand = new RelayCommand<GalleryItem>(item =>
            {
                if (item is null) return;
                item.IsSelected = !item.IsSelected;
                // make the toggled item the current item
                CurrentItem = item;
            });
            RemoveSelectedCommand = new RelayCommand(RemoveSelected);
            CycleThumbnailSizeCommand = new RelayCommand(CycleThumbnailSize);
            AdjustBrightnessCommand = new RelayCommand(() => { /* TODO: hook image service */ });
            AdjustContrastCommand = new RelayCommand(() => { /* TODO: hook image service */ });
            AssignTagToSelectedCommand = new RelayCommand<TagCategory>(AssignTagToSelected);
            AddTagCommand = new RelayCommand(AddTag);
            DeleteTagCommand = new RelayCommand<TagCategory>(DeleteTag);

        }

        public void AddImages(IEnumerable<string> imagePaths)
        {
            if (imagePaths == null) return;

            // Normalize, filter existing, and sort by filename
            var existing = new HashSet<string>(GalleryItems.Select(g => NormalizePath(g.ImagePath)), System.StringComparer.OrdinalIgnoreCase);

            var toAdd = imagePaths
                .Where(p => !string.IsNullOrWhiteSpace(p))
                .Select(NormalizePath)
                .Where(p => File.Exists(p))
                .Where(p => !existing.Contains(p))
                .Select(p => new GalleryItem
                {
                    ImagePath = p,
                    Name = Path.GetFileName(p),
                    IsSelected = false
                })
                .OrderBy(item => item.Name, System.StringComparer.OrdinalIgnoreCase)
                .ToList();

            foreach (var item in toAdd)
            {
                GalleryItems.Add(item);
            }

            // Default current item if none selected yet
            if (CurrentItem == null && GalleryItems.Count > 0)
            {
                CurrentItem = GalleryItems[0];
            }
            RaiseComputedChanged();
        }

        public void ClearImages()
        {
            GalleryItems.Clear();
            CurrentItem = null;
            RaiseComputedChanged();
        }

        public void AddImage(string imagePath)
        {
            if (string.IsNullOrWhiteSpace(imagePath)) return;
            AddImages(new[] { imagePath });
        }

        private static string NormalizePath(string path)
        {
            return Path.GetFullPath(path);
        }

        public void RemoveSelected()
        {
            var toRemove = GalleryItems.Where(i => i.IsSelected).ToList();
            foreach (var item in toRemove)
                GalleryItems.Remove(item);

            // adjust current item if it was removed
            if (CurrentItem != null && toRemove.Contains(CurrentItem))
            {
                CurrentItem = GalleryItems.FirstOrDefault();
            }
            RaiseComputedChanged();
        }

        public void CycleThumbnailSize()
        {
            // Cycle through predefined sizes
            var sizes = new[] { 110, 138, 170 };
            var idx = Array.IndexOf(sizes, ThumbnailSize);
            ThumbnailSize = sizes[(idx + 1 + sizes.Length) % sizes.Length];
        }

        // ===== Helpers for selection/computed updates =====
        public void SetCurrent(GalleryItem? item)
        {
            CurrentItem = item;
        }

        private void OnGalleryItemsChanged(object? sender, NotifyCollectionChangedEventArgs e)
        {
            if (e.NewItems != null)
            {
                foreach (var obj in e.NewItems)
                {
                    if (obj is GalleryItem gi)
                        gi.PropertyChanged += OnItemPropertyChanged;
                }
            }
            if (e.OldItems != null)
            {
                foreach (var obj in e.OldItems)
                {
                    if (obj is GalleryItem gi)
                        gi.PropertyChanged -= OnItemPropertyChanged;
                }
            }
            RaiseComputedChanged();
        }

        private void OnItemPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(GalleryItem.IsSelected) ||
                e.PropertyName == nameof(GalleryItem.Name) ||
                e.PropertyName == nameof(GalleryItem.ImagePath) ||
                e.PropertyName == nameof(GalleryItem.TagId))
            {
                RaiseComputedChanged();
            }
        }

        private void RaiseComputedChanged()
        {
            OnPropertyChanged(nameof(TotalCount));
            OnPropertyChanged(nameof(SelectedCount));
            OnPropertyChanged(nameof(CurrentIndex1Based));
            OnPropertyChanged(nameof(CurrentName));
            OnPropertyChanged(nameof(CurrentPath));
            OnPropertyChanged(nameof(ImageCountText));
            OnPropertyChanged(nameof(SelectedSummaryText));
            OnPropertyChanged(nameof(AnnotatedCount));
            OnPropertyChanged(nameof(AnnotationProgressPercent));
            OnPropertyChanged(nameof(AnnotationProgressText));
        }

        // ===== Tag operations =====
        private void AssignTagToSelected(TagCategory? tag)
        {
            if (tag == null) return;
            foreach (var it in GalleryItems.Where(i => i.IsSelected))
                it.TagId = tag.Id;
        }

        private void AddTag()
        {
            var name = string.IsNullOrWhiteSpace(NewTagName) ? $"Tag_{_nextTagId}" : NewTagName.Trim();
            var tag = new TagCategory
            {
                Id = _nextTagId++,
                Name = name,
                Color = NewTagColor,
                IconKind = NewTagIcon
            };
            Tags.Add(tag);
            NewTagName = string.Empty; // reset
        }

        private void DeleteTag(TagCategory? tag)
        {
            if (tag == null) return;
            if (tag.Id == 0) return; // keep 无标签
            // remove the tag first
            var removedId = tag.Id;
            Tags.Remove(tag);

            // Build mapping oldId -> newId, keep 0 reserved
            var map = new Dictionary<int, int>();
            map[0] = 0;
            int next = 1;
            foreach (var t in Tags.OrderBy(t => t.Id))
            {
                if (t.Id == 0) continue;
                map[t.Id] = next;
                t.Id = next;
                next++;
            }

            // Remap gallery items; any item with removedId should go to 0 (no tag);
            foreach (var it in GalleryItems)
            {
                if (it.TagId == removedId)
                {
                    it.TagId = 0;
                }
                else if (map.TryGetValue(it.TagId, out var newId))
                {
                    it.TagId = newId;
                }
            }

            // Update next tag id to be next
            _nextTagId = next;
        }
    }
}
